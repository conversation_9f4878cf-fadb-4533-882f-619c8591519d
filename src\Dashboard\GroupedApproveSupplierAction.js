import React, { useState, useEffect } from "react";
import APIServices from "../service/APIService";
import { API } from "../constants/api_url";
import { AttachmentComponent } from "../components/Forms/Attachment";

import { Card } from "primereact/card";
import { Divider } from "primereact/divider";
import { Button } from "primereact/button";
import { Message } from "primereact/message";
import { useSelector } from "react-redux";
import moment from "moment";
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Checkbox } from 'primereact/checkbox';
import { Badge } from 'primereact/badge';

// Mapping array for Supplier Category
const categoryList = [
    { name: 'Forging & Machining', value: 1 },
    { name: 'Casting & Machining', value: 2 },
    { name: 'Pressing & Fabrication', value: 3 },
    { name: 'Proprietary Mechanical', value: 4 },
    { name: 'Proprietary Electrical', value: 5 },
    { name: 'Plastics, Rubber, Painting and Stickers', value: 6 },
    { name: 'EV/3W/2W', value: 7 },
    { name: 'BW', value: 8 },
    { name: 'Accessories', value: 9 },
    { name: 'IDM (Indirect Material)', value: 10 },
    { name: 'Import', value: 11 }
];

const GroupedApproveSupplierAction = ({ actions, refresh }) => {
    const [selectedActions, setSelectedActions] = useState([]);
    const [commands, setCommands] = useState("");
    const [errors, setErrors] = useState({});
    const [activeIndex, setActiveIndex] = useState(0);
    const login_data = useSelector((state) => state.user.userdetail);
    const vendorCode = useSelector((state) => state.user.currentVendorCode);

    useEffect(() => {
        // Initialize with all actions selected except those that are already approved (type 3)
        setSelectedActions(actions.filter(action => action.type !== 3).map(action => action.id));
    }, [actions]);

    const nonComplianceOptions = [
        { label: "Regulatory (Major)", id: 1 },
        { label: "Regulatory (Minor)", id: 2 },
        { label: "Minor", id: 3 },
    ];

    // Validation
    const validateFields = () => {
        let newErrors = {};

        if (!commands.trim()) {
            newErrors.commands = "Comments field is required.";
        }

        if (selectedActions.length === 0) {
            newErrors.selectedActions = "Please select at least one action to approve/reject.";
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Function to handle action selection
    const onActionSelect = (actionId) => {
        // Find the action to check if it's already approved
        const action = actions.find(a => a.id === actionId);

        // Don't allow selection if action is already approved (type 3)
        if (action && action.type === 3) {
            return;
        }

        if (selectedActions.includes(actionId)) {
            setSelectedActions(selectedActions.filter(id => id !== actionId));
        } else {
            setSelectedActions([...selectedActions, actionId]);
        }
    };

    // Function to select/deselect all actions
    const toggleSelectAll = () => {
        // Get all selectable actions (exclude type 3)
        const selectableActions = actions.filter(action => action.type !== 3);

        if (selectedActions.length === selectableActions.length) {
            setSelectedActions([]);
        } else {
            setSelectedActions(selectableActions.map(action => action.id));
        }
    };

    // Common function to submit the patch call (approve = true or false)
    const handleSubmit = async (rejectValue) => {
        if (!validateFields()) return;

        try {
            // Process each selected action
            for (const actionId of selectedActions) {
                const action = actions.find(a => a.id === actionId);

                if (rejectValue === 0) {
                    // Approve action
                    await APIServices.patch(API.SupplierAction_Edit(action.id), {
                        approved_on: new Date().toISOString(),
                        approved_by: login_data.id,
                        type: 3,
                        reject: 0
                    });

                    await APIServices.patch(API.SupplierActionHistory_Edit(action.supplierActionHistories[action.supplierActionHistories.length - 1]?.id), {
                        approved_on: new Date().toISOString(),
                        approved_by: login_data.id,
                        type: 2,
                        reject: 0,
                        approverComments: commands
                    });
                } else if (rejectValue === 1) {
                    // Reject action
                    await APIServices.patch(API.SupplierAction_Edit(action.id), {
                        type: 1,
                        reject: 1
                    });

                    await APIServices.patch(API.SupplierActionHistory_Edit(action.supplierActionHistories[action.supplierActionHistories.length - 1]?.id), {
                        returned_on: new Date().toISOString(),
                        returned_by: login_data.id,
                        type: 1,
                        reject: 1,
                        approverComments: commands
                    });
                }
            }
        } catch (error) {
            console.error("Error processing actions:", error);
        } finally {
            refresh();
        }
    };

    // Function to render supplier metadata
    const renderSupplierMetadata = () => {
        if (!actions || actions.length === 0 || !actions[0].vendor) return null;

        const vendor = actions[0].vendor;

        // Map supplier category value to name
        const getSupplierCategoryName = (categoryValue) => {
            const category = categoryList.find(item => item.value === categoryValue);
            return category ? category.name : categoryValue;
        };

        return (
            <Card className="mb-4 shadow-sm">
                <h4 className="mb-3 clr-navy">Supplier Information</h4>
                <Divider />
                <div className="row">
                    <div className="col-md-4">
                        <p><strong>Supplier Name:</strong> {vendor.supplierName || 'N/A'}</p>
                        <p><strong>Supplier Code:</strong> {vendor.code || 'N/A'}</p>
                        <p><strong>Supplier Category:</strong> {getSupplierCategoryName(vendor.supplierCategory) || 'N/A'}</p>
                        <p><strong>Supplier Location:</strong> {vendor.supplierLocation || 'N/A'}</p>
                    </div>
                    <div className="col-md-4">
                        <p><strong>Supplier SPOC:</strong> {vendor.supplierSPOC || 'N/A'}</p>
                        <p><strong>Contact:</strong> {vendor.supplierContact || 'N/A'}</p>
                        <p><strong>Contact 2:</strong> {vendor.supplierContact2 || 'N/A'}</p>
                        <p><strong>Contact 3:</strong> {vendor.supplierContact3 || 'N/A'}</p>
                    </div>
                    <div className="col-md-4">
                        <p><strong>Email 2:</strong> {vendor.supplierEmail2 || 'N/A'}</p>
                        <p><strong>Email 3:</strong> {vendor.supplierEmail3 || 'N/A'}</p>
                        <p><strong>Spent On:</strong> {vendor.supplierSpentOn ? `${vendor.supplierSpentOn}%` : 'N/A'}</p>
                        <p><strong>Modified On:</strong> {vendor.modified_on ? moment(vendor.modified_on).format('DD-MM-YYYY') : 'N/A'}</p>
                    </div>
                </div>
            </Card>
        );
    };

    // Render action details in accordion
    const renderActionAccordion = () => {
        return (
            <Card className="mb-4 shadow-sm">
                <div className="d-flex justify-content-between align-items-center mb-3">
                    <h4 className="mb-0 clr-navy">
                        Actions ({actions.length})
                        {actions.some(action => action.type === 3) &&
                            <span className="text-muted fs-6 ml-2">
                                ({actions.filter(action => action.type !== 3).length} selectable)
                            </span>
                        }
                    </h4>
                    <div className="d-flex align-items-center">
                        <Checkbox
                            inputId="selectAll"
                            checked={selectedActions.length === actions.filter(action => action.type !== 3).length}
                            onChange={toggleSelectAll}
                        />
                        <label htmlFor="selectAll" className="ml-2">Select All</label>
                    </div>
                </div>
                <Divider />

                {errors.selectedActions && (
                    <Message severity="error" text={errors.selectedActions} className="mb-3" />
                )}

                <Accordion activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                    {actions.map((action, index) => {
                        const nonComplianceLabel = nonComplianceOptions.find(
                            (option) => option.id === action.nonComplianceType
                        )?.label || "Unknown";

                        const lastActionHistory = action.supplierActionHistories &&
                            action.supplierActionHistories.length > 0 ?
                            action.supplierActionHistories[action.supplierActionHistories.length - 1] : null;

                        return (
                            <AccordionTab
                                key={action.id}
                                header={
                                    <div className="d-flex align-items-center">
                                        <Checkbox
                                            inputId={`action-${action.id}`}
                                            checked={selectedActions.includes(action.id)}
                                            onChange={() => onActionSelect(action.id)}
                                            onClick={(e) => e.stopPropagation()}
                                            disabled={action.type === 3}
                                        />
                                        <span className="ml-2">Action ID: {action.actionId} - {action.finding}</span>
                                        {action.type === 3 && <Badge value="Approved" severity="success" className="ml-2" />}
                                    </div>
                                }
                            >
                                <div className="row">
                                    <div className="col-md-6">
                                        <p><strong>ID:</strong> {action.actionId}</p>
                                        <p><strong>Findings:</strong> {action.finding}</p>
                                        <p><strong>Description:</strong> {action.description}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p><strong>Non-Compliance Type:</strong> {nonComplianceLabel}</p>
                                    </div>
                                    <div className="col-md-6">
                                        <p><strong>Root Cause (As per Action Plan):</strong>  <div dangerouslySetInnerHTML={{ __html: action.rootCause }}></div></p>
                                        <p><strong>Proposed Corrective Action (As per Action Plan):</strong> <div dangerouslySetInnerHTML={{ __html: action.proposedCorrectiveAction }}></div></p>
                                    </div>
                                </div>

                                {lastActionHistory && (
                                    <>
                                        <h5 className="mt-3">Root Cause</h5>
                                        <div
                                            className="border p-2 mb-3"
                                            style={{ backgroundColor: "#f8f9fa" }}
                                            dangerouslySetInnerHTML={{ __html: lastActionHistory.rootCause || "Not provided" }}
                                        />

                                        <h5>Corrective Actions</h5>
                                        <div
                                            className="border p-2 mb-3"
                                            style={{ backgroundColor: "#f8f9fa" }}
                                            dangerouslySetInnerHTML={{ __html: lastActionHistory.correctiveAction || "Not provided" }}
                                        />

                                        <h5>Action Taken</h5>
                                        <div
                                            className="border p-2 mb-3"
                                            style={{ backgroundColor: "#f8f9fa" }}
                                            dangerouslySetInnerHTML={{ __html: lastActionHistory.actionTaken || "Not provided" }}
                                        />

                                        {lastActionHistory.supplierAttachments && lastActionHistory.supplierAttachments.length > 0 && (
                                            <div className="mt-3">
                                                <h5>Attachments</h5>
                                                <AttachmentComponent
                                                    mandatory={false}
                                                    edit={0}
                                                    documents={lastActionHistory.supplierAttachments}
                                                />
                                            </div>
                                        )}
                                    </>
                                )}
                            </AccordionTab>
                        );
                    })}
                </Accordion>
            </Card>
        );
    };

    return (
        <div className="container-fluid">
            {/* Supplier Metadata */}
            {renderSupplierMetadata()}

            {/* Action Accordion */}
            {renderActionAccordion()}

            {/* Comments Textarea */}
            <Card className="mb-4 shadow-sm">
                <h4 className="mb-3 clr-navy">Comments</h4>
                <Divider />
                <textarea
                    className="form-control"
                    rows={3}
                    value={commands}
                    onChange={(e) => setCommands(e.target.value)}
                    placeholder="Enter your comments here..."
                />
                {errors.commands && <Message severity="error" text={errors.commands} />}
            </Card>

            {/* Approve / Reject Buttons */}
            <div className="flex justify-content-end mt-3 gap-2">
                <Button
                    label={`Approve Selected (${selectedActions.length}/${actions.filter(action => action.type !== 3).length})`}
                    icon="pi pi-check"
                    className="p-button-success"
                    onClick={() => handleSubmit(0)}
                    disabled={selectedActions.length === 0}
                />
                <Button
                    label={`Reject Selected (${selectedActions.length}/${actions.filter(action => action.type !== 3).length})`}
                    icon="pi pi-times"
                    className="p-button-danger"
                    onClick={() => handleSubmit(1)}
                    disabled={selectedActions.length === 0}
                />
            </div>
        </div>
    );
};

export default GroupedApproveSupplierAction;
